# WWW Redirect Configuration

This document explains the WWW canonicalization setup implemented to fix image loading issues between www and non-www website access.

## Problem
When users access the website with or without the "www." prefix, images and assets may not load correctly due to URL mismatches between the configured `APP_URL` and the actual request URL.

## Solution
A comprehensive WWW canonicalization system has been implemented with multiple layers:

### 1. Middleware Solution (`WWWRedirectMiddleware`)
- **Location**: `app/Http/Middleware/WWWRedirectMiddleware.php`
- **Purpose**: Handles URL canonicalization at the application level
- **Features**:
  - Redirects between www and non-www versions based on configuration
  - Skips redirects for local development, API routes, and asset requests
  - Dynamically updates `APP_URL` to match current request
  - Ensures consistent asset URL generation

### 2. Configuration
- **Location**: `config/app.php`
- **New Setting**: `force_www` (controlled by `FORCE_WWW` environment variable)
- **Default**: `false` (redirects www to non-www)
- **Options**:
  - `true`: Forces www prefix (redirects non-www to www)
  - `false`: Forces non-www (redirects www to non-www)

### 3. Server-Level Rules (.htaccess)
- **Location**: `public/.htaccess`
- **Purpose**: Provides server-level canonicalization as backup
- **Current Setting**: Forces non-www (www redirects to non-www)
- **Note**: Can be switched by commenting/uncommenting the appropriate blocks

### 4. Asset URL Configuration
- **Location**: `app/Providers/AppServiceProvider.php`
- **Purpose**: Ensures asset URLs match the current request domain
- **Method**: `configureAssetUrls()` automatically sets asset URL if not configured

## Configuration Options

### Environment Variables
Add to your `.env` file:

```env
# Force WWW redirect behavior
FORCE_WWW=false  # Set to true to force www, false to force non-www

# Optionally set explicit asset URL
ASSET_URL=https://yourdomain.com
```

### .htaccess Configuration
In `public/.htaccess`, choose one option:

**Option 1: Force WWW (uncomment these lines)**
```apache
RewriteCond %{HTTP_HOST} !^www\. [NC]
RewriteCond %{HTTP_HOST} !^localhost [NC]
RewriteCond %{HTTP_HOST} !^127\.0\.0\.1 [NC]
RewriteRule ^(.*)$ https://www.%{HTTP_HOST}%{REQUEST_URI} [R=301,L]
```

**Option 2: Force non-WWW (currently active)**
```apache
RewriteCond %{HTTP_HOST} ^www\.(.+)$ [NC]
RewriteCond %{HTTP_HOST} !^localhost [NC]
RewriteCond %{HTTP_HOST} !^127\.0\.0\.1 [NC]
RewriteRule ^(.*)$ https://%1%{REQUEST_URI} [R=301,L]
```

## How It Works

1. **Request Processing**:
   - User accesses site with www or non-www
   - `WWWRedirectMiddleware` checks the configuration
   - If URL doesn't match canonical format, user is redirected (301)
   - Asset URLs are dynamically updated to match current domain

2. **Asset Generation**:
   - `asset()` function uses the dynamically updated `APP_URL`
   - All images and assets load from the canonical domain
   - No more broken images due to www/non-www mismatches

3. **Fallback Protection**:
   - .htaccess rules provide server-level protection
   - AppServiceProvider ensures asset URLs are always correct
   - Multiple layers prevent any canonicalization issues

## Testing

To test the implementation:

1. Access your site with www: `https://www.yourdomain.com`
2. Access your site without www: `https://yourdomain.com`
3. Verify that:
   - You're redirected to the canonical version
   - All images load correctly on both versions
   - Asset URLs are consistent

## Troubleshooting

### Images Still Not Loading
1. Check your `APP_URL` in `.env`
2. Verify the `FORCE_WWW` setting matches your preference
3. Clear application cache: `php artisan config:clear`
4. Check .htaccess rules are active

### Local Development Issues
The middleware automatically skips redirects for:
- localhost
- 127.0.0.1
- .local, .test, .dev domains

### Performance Considerations
- Redirects are 301 (permanent) for SEO benefits
- Middleware runs early in the request cycle
- Asset URL configuration happens once per request

## Files Modified

1. `app/Http/Middleware/WWWRedirectMiddleware.php` (new)
2. `app/Http/Kernel.php` (middleware registration)
3. `config/app.php` (force_www configuration)
4. `public/.htaccess` (server-level rules)
5. `app/Providers/AppServiceProvider.php` (asset URL configuration)
