<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # WWW Canonicalization (uncomment one of the following blocks)
    # Option 1: Force WWW (redirect non-www to www)
    # RewriteCond %{HTTP_HOST} !^www\. [NC]
    # RewriteCond %{HTTP_HOST} !^localhost [NC]
    # RewriteCond %{HTTP_HOST} !^127\.0\.0\.1 [NC]
    # RewriteRule ^(.*)$ https://www.%{HTTP_HOST}%{REQUEST_URI} [R=301,L]

    # Option 2: Force non-WWW (redirect www to non-www) - DEFAULT
    RewriteCond %{HTTP_HOST} ^www\.(.+)$ [NC]
    RewriteCond %{HTTP_HOST} !^localhost [NC]
    RewriteCond %{HTTP_HOST} !^127\.0\.0\.1 [NC]
    RewriteRule ^(.*)$ https://%1%{REQUEST_URI} [R=301,L]

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>
