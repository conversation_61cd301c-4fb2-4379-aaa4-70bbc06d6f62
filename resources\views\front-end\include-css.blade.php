<link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap"
    rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Asap:ital,wght@0,100..900;1,100..900&display=swap"
    rel="stylesheet">
<link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet" />

<script src="{{ asset('assets/front-end/assets/js/loopple/kit.fontawesome.js') }}" crossorigin="anonymous"></script>

<link href="{{ asset('assets/front-end/assets/css/nucleo-icons.css') }}" rel="stylesheet" />
<link href="{{ asset('assets/front-end/assets/css/nucleo-svg.css') }}" rel="stylesheet" />
{{--
<link rel="stylesheet" href="{{ asset('assets/front-end/assets/css/theme.css') }}"> --}}
@if($active_theme === 'old' || (isset($theme) && $theme === 'old'))
    <!-- Old Theme Styles -->
    <link rel="stylesheet" href="{{ asset('assets/front-end/assets/old/css/theme.css') }}">
@else
    <!-- New Theme Styles (Current) -->
    <link rel="stylesheet" href="{{ asset('assets/front-end/assets/css/theme.css') }}?v={{ time() }}">
@endif
<link rel="stylesheet" href="{{ asset('assets/front-end/assets/css/loopple/loopple.css') }}">
<link rel="stylesheet" href="{{ asset('assets/front-end/assets/css/custom.css') }}?v={{ time() }}">

<!-- MOBILE RESPONSIVE FIXES - DIRECT IMPLEMENTATION -->
<style>
/* MOBILE ONLY STYLES - FORCE OVERRIDE */
@media screen and (max-width: 768px) {
    /* MOBILE STICKY HEADER - SIMPLIFIED APPROACH */
    nav.navbar, .navbar, #mainNavbar {
        /* JavaScript will handle all positioning and transitions */
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        will-change: transform, position, background, box-shadow !important;
        padding: 0.75rem 0 !important;
        margin: 0 !important;
    }

    /* Ensure navbar container spans full width */
    .navbar .container,
    .navbar .container-fluid {
        width: 100% !important;
        max-width: 100% !important;
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }

    /* Ensure navbar brand and navigation items are properly positioned */
    .navbar-brand,
    .navbar-nav,
    .navbar-toggler {
        position: relative !important;
        z-index: 10001 !important;
    }

    /* ANIMATION KEYFRAMES FOR STICKY TRANSITION */
    @keyframes stickySlideIn {
        0% {
            transform: translateY(-10px) !important;
            opacity: 0.8 !important;
        }
        100% {
            transform: translateY(0) !important;
            opacity: 1 !important;
        }
    }

    /* PRICING PLAN BUTTON COLORS - ALL TABS (MONTHLY, ANNUAL, LIFETIME) */

    /* 1ST CARD - Light-Medium Green Button */
    .pricing-card:nth-child(1) .btn,
    .pricing-card:nth-child(1) .btn-primary,
    .tab-pane .pricing-card:nth-child(1) .btn,
    .tab-content .pricing-card:nth-child(1) .btn,
    #monthly .pricing-card:nth-child(1) .btn,
    #annual .pricing-card:nth-child(1) .btn,
    #lifetime .pricing-card:nth-child(1) .btn,
    .btn-plan-1,
    .btn-outline-plan-1 {
        background: linear-gradient(135deg, #52C41A 0%, #73D13D 100%) !important;
        border-color: #52C41A !important;
        color: #ffffff !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
    }

    .pricing-card:nth-child(1) .btn:hover,
    .pricing-card:nth-child(1) .btn-primary:hover,
    .tab-pane .pricing-card:nth-child(1) .btn:hover,
    .tab-content .pricing-card:nth-child(1) .btn:hover,
    #monthly .pricing-card:nth-child(1) .btn:hover,
    #annual .pricing-card:nth-child(1) .btn:hover,
    #lifetime .pricing-card:nth-child(1) .btn:hover,
    .btn-plan-1:hover,
    .btn-outline-plan-1:hover {
        background: linear-gradient(135deg, #389E0D 0%, #52C41A 100%) !important;
        border-color: #389E0D !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(82, 196, 26, 0.4) !important;
    }

    /* 2ND CARD - Light-Medium Orange Button */
    .pricing-card:nth-child(2) .btn,
    .pricing-card:nth-child(2) .btn-primary,
    .tab-pane .pricing-card:nth-child(2) .btn,
    .tab-content .pricing-card:nth-child(2) .btn,
    #monthly .pricing-card:nth-child(2) .btn,
    #annual .pricing-card:nth-child(2) .btn,
    #lifetime .pricing-card:nth-child(2) .btn,
    .btn-plan-2,
    .btn-outline-plan-2 {
        background: linear-gradient(135deg, #FF8C00 0%, #FFA940 100%) !important;
        border-color: #FF8C00 !important;
        color: #ffffff !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
    }

    .pricing-card:nth-child(2) .btn:hover,
    .pricing-card:nth-child(2) .btn-primary:hover,
    .tab-pane .pricing-card:nth-child(2) .btn:hover,
    .tab-content .pricing-card:nth-child(2) .btn:hover,
    #monthly .pricing-card:nth-child(2) .btn:hover,
    #annual .pricing-card:nth-child(2) .btn:hover,
    #lifetime .pricing-card:nth-child(2) .btn:hover,
    .btn-plan-2:hover,
    .btn-outline-plan-2:hover {
        background: linear-gradient(135deg, #D46B08 0%, #FF8C00 100%) !important;
        border-color: #D46B08 !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(255, 140, 0, 0.4) !important;
    }

    /* 3RD CARD - Light-Medium Blue Button */
    .pricing-card:nth-child(3) .btn,
    .pricing-card:nth-child(3) .btn-primary,
    .tab-pane .pricing-card:nth-child(3) .btn,
    .tab-content .pricing-card:nth-child(3) .btn,
    #monthly .pricing-card:nth-child(3) .btn,
    #annual .pricing-card:nth-child(3) .btn,
    #lifetime .pricing-card:nth-child(3) .btn,
    .btn-plan-3,
    .btn-outline-plan-3 {
        background: linear-gradient(135deg, #1890FF 0%, #40A9FF 100%) !important;
        border-color: #1890FF !important;
        color: #ffffff !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
    }

    .pricing-card:nth-child(3) .btn:hover,
    .pricing-card:nth-child(3) .btn-primary:hover,
    .tab-pane .pricing-card:nth-child(3) .btn:hover,
    .tab-content .pricing-card:nth-child(3) .btn:hover,
    #monthly .pricing-card:nth-child(3) .btn:hover,
    #annual .pricing-card:nth-child(3) .btn:hover,
    #lifetime .pricing-card:nth-child(3) .btn:hover,
    .btn-plan-3:hover,
    .btn-outline-plan-3:hover {
        background: linear-gradient(135deg, #096DD9 0%, #1890FF 100%) !important;
        border-color: #096DD9 !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(24, 144, 255, 0.4) !important;
    }

    /* HIDE FLOATING HERO IMAGES */
    .floating-card.top-right,
    .floating-card.bottom-left,
    .floating-card,
    img[src*="home_hero1.png"],
    img[src*="home_hero2.png"],
    img[src*="assets/front-end/img/gallery/home_hero1.png"],
    img[src*="assets/front-end/img/gallery/home_hero2.png"] {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
    }

    /* HIDE TO TOP BUTTON */
    #backToTopBtn,
    .back-to-top,
    [id*="backToTop"],
    [class*="back-to-top"] {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
    }

    /* REDUCE HERO SECTION SPACING */
    .hero-images-section,
    .carousel-section,
    section[class*="hero"] {
        padding-top: 1rem !important;
        padding-bottom: 0.5rem !important;
        margin-top: 0 !important;
        margin-bottom: 0.5rem !important;
    }

    .main-title,
    h1.main-title,
    .hero-images-section h1,
    .carousel-section h1 {
        margin-bottom: 1rem !important;
        font-size: 1.8rem !important;
        padding: 0 1rem !important;
    }

    /* MOBILE IMAGE OPTIMIZATION - ENHANCED SIZE AND POSITION */
    .main-image-container {
        padding: 0 0.5rem !important;
        max-width: 100% !important;
        margin-top: 38px !important; /* Move image 1cm (38px) downward */
        transform: translateY(38px) !important; /* Additional downward positioning */
    }

    .main-image-container img {
        width: calc(100% + 75px) !important; /* Increase width by 2cm (75px) */
        height: auto !important;
        min-height: calc(100% + 75px) !important; /* Increase height by 2cm (75px) */
        border-radius: 0.75rem !important;
        max-width: none !important; /* Allow image to exceed container width */
        margin-left: -37.5px !important; /* Center the enlarged image */
        object-fit: cover !important; /* Maintain aspect ratio while filling space */
    }


}

/* FORCE MOBILE DETECTION */
@media screen and (max-width: 767px) {
    /* Additional mobile targeting */
    .navbar-brand,
    .navbar-nav,
    .navbar-toggler {
        position: relative !important;
        z-index: 10000 !important;
    }
}
</style>

<!-- MOBILE STICKY HEADER JAVASCRIPT - FIXED VERSION -->
<script>
document.addEventListener('DOMContentLoaded', function() {

    function isMobile() {
        return window.innerWidth <= 768;
    }

    function initMobileStickyHeader() {
        if (!isMobile()) return;

        const navbar = document.querySelector('.navbar') || document.querySelector('#mainNavbar') || document.querySelector('nav.navbar');
        const body = document.body;

        if (!navbar) return;

        let isSticky = false;
        let navbarHeight = 0;
        let triggerPoint = 100; // Scroll 100px to trigger sticky

        // Force initial state
        navbar.style.cssText = `
            position: relative !important;
            top: auto !important;
            background: #ffffff !important;
            box-shadow: none !important;
            backdrop-filter: none !important;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        `;

        function measureNavbar() {
            navbarHeight = navbar.offsetHeight || 70;
        }

        function makeSticky() {
            if (isSticky) return;
            isSticky = true;

            // Add padding to body to prevent jump
            body.style.paddingTop = navbarHeight + 'px';
            body.style.transition = 'padding-top 0.3s ease';

            // Make navbar sticky with immediate fixed position
            navbar.style.cssText = `
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                width: 100vw !important;
                z-index: 9999 !important;
                background: rgba(255, 255, 255, 0.98) !important;
                backdrop-filter: blur(20px) !important;
                -webkit-backdrop-filter: blur(20px) !important;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
                transform: translateY(0) !important;
                transition: none !important;
                padding: 0.75rem 0 !important;
                margin: 0 !important;
            `;

            // Add smooth entrance animation
            navbar.style.animation = 'stickySlideIn 0.3s ease-out forwards';
        }

        function makeNormal() {
            if (!isSticky) return;
            isSticky = false;

            // Remove body padding
            body.style.paddingTop = '0px';

            // Return to normal position
            navbar.style.cssText = `
                position: relative !important;
                top: auto !important;
                left: auto !important;
                right: auto !important;
                width: 100% !important;
                z-index: 1030 !important;
                background: #ffffff !important;
                backdrop-filter: none !important;
                box-shadow: none !important;
                transform: translateY(0) !important;
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
            `;
        }

        function handleScroll() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            if (scrollTop > triggerPoint && !isSticky) {
                makeSticky();
            } else if (scrollTop <= (triggerPoint - 20) && isSticky) {
                // Add small buffer to prevent flickering
                makeNormal();
            }
        }

        // Debounced scroll handler for stability
        let scrollTimeout;
        function onScroll() {
            if (isMobile()) {
                clearTimeout(scrollTimeout);
                scrollTimeout = setTimeout(handleScroll, 10);
            }
        }

        // Initialize
        measureNavbar();

        // Attach listeners
        window.addEventListener('scroll', onScroll, { passive: true });

        // Handle resize
        window.addEventListener('resize', () => {
            if (isMobile()) {
                measureNavbar();
                handleScroll();
            } else {
                // Reset on desktop
                makeNormal();
            }
        });
    }

    // Initialize mobile sticky header
    initMobileStickyHeader();

    // Re-initialize on resize if switching to mobile
    window.addEventListener('resize', () => {
        setTimeout(initMobileStickyHeader, 100);
    });
});
</script>

<!-- Theme Color Overrides - Load Last -->
@if($active_theme !== 'old' && (!isset($theme) || $theme !== 'old'))
<style>
/* Professional Red Theme Overrides - Ensure these load last */
:root {
    --gohub-primary: #D32F2F !important;
    --gohub-secondary: #B71C1C !important;
    --gohub-primary-rgb: 211, 47, 47 !important;
    --gohub-secondary-rgb: 183, 28, 28 !important;
}

.btn-primary, .btn.btn-primary, .btn-btn-primary {
    background-color: #D32F2F !important;
    border-color: #D32F2F !important;
    color: #ffffff !important;
}

.btn-primary:hover, .btn.btn-primary:hover, .btn-btn-primary:hover {
    background-color: #B71C1C !important;
    border-color: #B71C1C !important;
    color: #ffffff !important;
}

.btn-secondary, .btn.btn-secondary {
    background-color: #B71C1C !important;
    border-color: #B71C1C !important;
    color: #ffffff !important;
}

.btn-secondary:hover, .btn.btn-secondary:hover {
    background-color: #8D1515 !important;
    border-color: #8D1515 !important;
    color: #ffffff !important;
}

.bg-primary {
    background-color: #D32F2F !important;
}

.bg-secondary {
    background-color: #B71C1C !important;
}

.text-primary {
    color: #D32F2F !important;
}

.text-secondary {
    color: #B71C1C !important;
}

.navbar-brand {
    color: #D32F2F !important;
}

.nav-pills .nav-link.active {
    background-color: #D32F2F !important;
    color: #ffffff !important;
}

/* Theme icon update for modern theme */
.theme-modern .theme-icon {
    background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%) !important;
}

/* Specific Homepage and Header Button Styling */
/* Get Started button on homepage */
.btn-gradient-dark {
    background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%) !important;
    border: none !important;
    color: #ffffff !important;
}

.btn-gradient-dark:hover {
    background: linear-gradient(135deg, #B71C1C 0%, #8D1515 100%) !important;
    color: #ffffff !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(211, 47, 47, 0.3) !important;
}

/* Login button in header - only this button, not the whole header */
.btn-outline-login {
    background-color: transparent !important;
    border: 2px solid #D32F2F !important;
    color: #D32F2F !important;
}

.btn-outline-login:hover {
    background-color: #D32F2F !important;
    border-color: #D32F2F !important;
    color: #ffffff !important;
}

/* Ensure header background stays normal (not red) */
.navbar.bg-primary {
    background-color: #ffffff !important;
    background: #ffffff !important;
}

/* Keep navbar brand and other elements normal */
.navbar-brand {
    color: #333333 !important;
}

.nav-link {
    color: #333333 !important;
}

.nav-link:hover {
    color: #D32F2F !important;
}

/* Override for pricing tabs specifically */
ul.pricing-tabs .nav-link {
    color: #333333 !important;
}

ul.pricing-tabs .nav-link:hover {
    color: #000000 !important;
}

/* Ensure all red buttons have white text for visibility */
.btn-plan-1, .btn-plan-2, .btn-plan-3, .btn-plan-4, .btn-plan-5,
.btn-outline-plan-1, .btn-outline-plan-2, .btn-outline-plan-3, .btn-outline-plan-4, .btn-outline-plan-5 {
    color: #ffffff !important;
}

.btn-outline-plan-1:hover, .btn-outline-plan-2:hover, .btn-outline-plan-3:hover, .btn-outline-plan-4:hover, .btn-outline-plan-5:hover {
    color: #ffffff !important;
}

/* Make outline plan buttons (free plans) have same background as solid plan buttons */
.btn-outline-plan-1, .btn-outline-plan-2, .btn-outline-plan-3, .btn-outline-plan-4, .btn-outline-plan-5 {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
    color: #ffffff !important;
}

.btn-outline-plan-1:hover, .btn-outline-plan-2:hover, .btn-outline-plan-3:hover, .btn-outline-plan-4:hover, .btn-outline-plan-5:hover {
    background-color: #218838 !important;
    border-color: #218838 !important;
    color: #ffffff !important;
}

/* Additional red button classes that might need white text */
.bg-gradient-dark, .btn.bg-gradient-dark {
    color: #ffffff !important;
}

/* Ensure any button with red background has white text */
[class*="btn-plan-"], [class*="btn-outline-plan-"] {
    color: #ffffff !important;
}

/* Monthly, Annual, Lifetime tab buttons styling - More specific selectors */
ul.pricing-tabs.nav-pills .nav-link {
    color: #333333 !important; /* Default text color */
}

ul.pricing-tabs.nav-pills .nav-link:hover {
    color: #000000 !important; /* Black text on hover */
}

ul.pricing-tabs.nav-pills .nav-link.active,
ul.pricing-tabs.nav-pills .nav-link.active:focus {
    background-color: #D32F2F !important; /* Red background when selected */
    color: #ffffff !important; /* White text when selected */
    border-color: #D32F2F !important;
}

ul.pricing-tabs.nav-pills .nav-link.active:hover {
    background-color: #D32F2F !important;
    color: #ffffff !important; /* Keep white text on hover when active */
}

/* Additional Get Started buttons throughout the site */
.btn-primary.btn-lg {
    background-color: #D32F2F !important;
    border-color: #D32F2F !important;
    color: #ffffff !important;
}

.btn-primary.btn-lg:hover {
    background-color: #B71C1C !important;
    border-color: #B71C1C !important;
    color: #ffffff !important;
}

/* Footer Copyright Section - Black background with white text */
.footer .row.text-center:last-child {
    background-color: #000000 !important;
    color: #ffffff !important;
    padding: 15px 20px !important;
    margin: 0 -15px !important;
    border-radius: 8px !important;
}

.footer .row.text-center:last-child p {
    color: #ffffff !important;
    margin-bottom: 0 !important;
}

.footer .row.text-center:last-child .col-md-auto {
    color: #ffffff !important;
}

/* Ensure any links in footer text are white */
.footer .row.text-center:last-child a {
    color: #ffffff !important;
    text-decoration: underline !important;
}

.footer .row.text-center:last-child a:hover {
    color: #cccccc !important;
}

/* Login page - Main heading styling only */
/* Target only the "Login or Register" heading, not the subheading */
.section-py .container h1.display-5.fw-semi-bold.mt-5.text-center {
    color: #007bff !important; /* Blue color */
    font-weight: bold !important; /* Bold text */
}

/* ===== NESTKO CHATBOT WIDGET STYLES ===== */
.nestko-chat-widget {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.chat-bubble-user {
    background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%);
    color: white;
    margin-left: 2rem;
    border-radius: 1rem 1rem 0.25rem 1rem;
    max-width: 280px;
    padding: 1rem;
    box-shadow: 0 4px 12px rgba(211, 47, 47, 0.3);
    transform: translateY(0);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chat-bubble-assistant {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    color: #2c3e50;
    margin-right: 2rem;
    border-radius: 1rem 1rem 1rem 0.25rem;
    max-width: 280px;
    padding: 1rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #e1e8ed;
    transform: translateY(0);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chat-input {
    border-radius: 9999px;
    border: 1px solid #d1d5db;
    background: #f9fafb;
    padding: 0.5rem 1rem;
    padding-right: 3rem;
    width: 100%;
}

.chat-input:focus {
    outline: none;
    ring: 2px;
    ring-color: #3b82f6;
    border-color: transparent;
}

.quick-action-btn {
    background: white;
    border: 1px solid #e5e7eb;
    color: #374151;
    border-radius: 9999px;
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    transition: all 0.2s;
    cursor: pointer;
}

.quick-action-btn:hover {
    background: #f9fafb;
}

/* Enhanced Widget Main Container */
.widget-main {
    width: 400px;
    height: 680px;
    background: white;
    border-radius: 1.25rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05);
    display: none;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid #e1e8ed;
    backdrop-filter: blur(10px);
}

/* Enhanced Avatar Containers */
.avatar-container {
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(211, 47, 47, 0.3);
}

.avatar-container .avatar-content {
    width: 1.75rem;
    height: 1.75rem;
    background-color: white;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.avatar-container .avatar-text {
    color: #D32F2F;
    font-weight: bold;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

/* Enhanced Header Styling */
.widget-header {
    background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%);
    color: white;
    padding: 2rem 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    min-height: 140px;
}

.chat-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-bottom: 1px solid #e1e8ed;
    padding: 1.25rem 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Enhanced Chat Area */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
    background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
    min-height: 350px;
}

.chat-input-area {
    padding: 1.25rem 1.5rem;
    background: white;
    border-top: 1px solid #e1e8ed;
}

/* Enhanced Animation classes */
.fade-in {
    animation: fadeIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-up {
    animation: slideUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.bounce-dot {
    animation: bounce 1.4s ease-in-out infinite both;
    width: 0.5rem;
    height: 0.5rem;
    background: #D32F2F;
    border-radius: 50%;
}

.bounce-dot:nth-child(1) { animation-delay: -0.32s; }
.bounce-dot:nth-child(2) { animation-delay: -0.16s; }

/* Enhanced Blinking eye animation for chatbot trigger */
.chatbot-eye {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chatbot-eye-blink {
    animation: eyeBlink 0.4s ease-in-out;
}

.chatbot-trigger-loaded {
    animation: initialBlink 4s ease-in-out;
}

.chatbot-face {
    transition: all 0.3s ease;
}

.chatbot-face:hover {
    transform: scale(1.05);
}

.chatbot-face:hover .chatbot-eye {
    transform: scaleY(0.7);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px) rotateX(-10deg) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) rotateX(0) scale(1);
    }
}

@keyframes bounce {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

@keyframes eyeBlink {
    0%, 100% { transform: scaleY(1); }
    50% { transform: scaleY(0.2); }
}

@keyframes initialBlink {
    0%, 8%, 16%, 24%, 32%, 40% { transform: scaleY(1); }
    4%, 12%, 20%, 28% { transform: scaleY(0.2); }
    50%, 100% { transform: scaleY(1); }
}

/* Widget positioning - 1.4cm (53px) away from back to top button */
.widget-container {
    position: fixed;
    bottom: 1.5rem;
    right: 6.5rem; /* Positioned 1.4cm (53px) to the left of back to top button */
    z-index: 9998; /* One level below back to top button */
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.widget-container:hover {
    transform: translateY(-2px);
}

/* Responsive design */
@media (max-width: 640px) {
    .widget-container {
        bottom: 1rem;
        right: 1rem;
        left: auto;
    }

    .chat-widget-main {
        width: calc(100vw - 2rem) !important;
        height: 500px !important;
    }
}

/* Laravel specific utilities */
.laravel-btn {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border: 1px solid transparent;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.375rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    color: white;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
}

.laravel-btn-primary {
    background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%);
    border-color: #D32F2F;
    box-shadow: 0 4px 12px rgba(211, 47, 47, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.laravel-btn-primary:hover {
    background: linear-gradient(135deg, #B71C1C 0%, #8D1515 100%);
    border-color: #B71C1C;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(211, 47, 47, 0.4);
}

.laravel-btn-primary:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(211, 47, 47, 0.3);
}

.laravel-btn-secondary {
    background-color: #6b7280;
    border-color: #6b7280;
}

.laravel-btn-secondary:hover {
    background-color: #4b5563;
    border-color: #4b5563;
}

.laravel-btn-secondary:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(107, 114, 128, 0.5);
}

.laravel-input {
    display: block;
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    font-size: 0.875rem;
    line-height: 1.25rem;
}

.laravel-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.laravel-card {
    background-color: white;
    overflow: hidden;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    border-radius: 0.5rem;
}

.laravel-card-body {
    padding: 1rem 1.5rem;
}

/* Enhanced Custom Icons using CSS */
.icon-send {
    width: 18px;
    height: 18px;
    background: currentColor;
    mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M3.4 20.4l17.45-7.48c.81-.35.81-1.49 0-1.84L3.4 3.6c-.66-.29-1.39.2-1.39.95L2 9.12c0 .5.37.93.87.99L17 12 2.87 13.88c-.5.07-.87.49-.87.99l.01 4.57c0 .75.73 1.24 1.39.95z'/%3E%3C/svg%3E") no-repeat center;
    mask-size: contain;
}

.icon-emoji {
    width: 18px;
    height: 18px;
    background: currentColor;
    mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM8.5 9c.83 0 1.5.67 1.5 1.5S9.33 12 8.5 12 7 11.33 7 10.5 7.67 9 8.5 9zm7 0c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5-1.5-.67-1.5-1.5S14.67 9 15.5 9zm-3.5 6.5c2.33 0 4.31-1.46 5.11-3.5H6.89c.8 2.04 2.78 3.5 5.11 3.5z'/%3E%3C/svg%3E") no-repeat center;
    mask-size: contain;
}

.icon-close {
    width: 16px;
    height: 16px;
    background: currentColor;
    mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M18.3 5.71c-.39-.39-1.02-.39-1.41 0L12 10.59 7.11 5.7c-.39-.39-1.02-.39-1.41 0-.39.39-.39 1.02 0 1.41L10.59 12 5.7 16.89c-.39.39-.39 1.02 0 1.41.39.39 1.02.39 1.41 0L12 13.41l4.89 4.89c.39.39 1.02.39 1.41 0 .39-.39.39-1.02 0-1.41L13.41 12l4.89-4.89c.38-.38.38-1.02 0-1.4z'/%3E%3C/svg%3E") no-repeat center;
    mask-size: contain;
}

.icon-minimize {
    width: 16px;
    height: 16px;
    background: currentColor;
    mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M6 19h12c.55 0 1-.45 1-1s-.45-1-1-1H6c-.55 0-1 .45-1 1s.45 1 1 1z'/%3E%3C/svg%3E") no-repeat center;
    mask-size: contain;
}

.icon-new-chat {
    width: 16px;
    height: 16px;
    background: currentColor;
    mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm4 11h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3H8c-.55 0-1-.45-1-1s.45-1 1-1h3V8c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z'/%3E%3C/svg%3E") no-repeat center;
    mask-size: contain;
}

.icon-rephrase {
    width: 16px;
    height: 16px;
    background: currentColor;
    mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 6v3l4-4-4-4v3c-4.42 0-8 3.58-8 8 0 1.57.46 3.03 1.24 4.26L6.7 14.8c-.45-.83-.7-1.79-.7-2.8 0-3.31 2.69-6 6-6zm6.76 1.74L17.3 9.2c.44.84.7 1.79.7 2.8 0 3.31-2.69 6-6 6v-3l-4 4 4 4v-3c4.42 0 8-3.58 8-8 0-1.57-.46-3.03-1.24-4.26z'/%3E%3C/svg%3E") no-repeat center;
    mask-size: contain;
}

/* Enhanced Avatar Icons */
.avatar-icon {
    width: 20px;
    height: 20px;
    background: currentColor;
    mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L9 7V9C9 10.1 9.9 11 11 11V14L13 16L15 14V11C16.1 11 17 10.1 17 9Z'/%3E%3C/svg%3E") no-repeat center;
    mask-size: contain;
}

.chat-icon {
    width: 22px;
    height: 22px;
    background: currentColor;
    mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-3 12H7c-.55 0-1-.45-1-1s.45-1 1-1h10c.55 0 1 .45 1 1s-.45 1-1 1zm0-3H7c-.55 0-1-.45-1-1s.45-1 1-1h10c.55 0 1 .45 1 1s-.45 1-1 1zm0-3H7c-.55 0-1-.45-1-1s.45-1 1-1h10c.55 0 1 .45 1 1s-.45 1-1 1z'/%3E%3C/svg%3E") no-repeat center;
    mask-size: contain;
}

/* Enhanced chat input styling */
.chat-input {
    border-radius: 25px;
    border: 2px solid #e1e8ed;
    background: #ffffff;
    padding: 0.75rem 1rem;
    padding-right: 3rem;
    width: 100%;
    font-size: 0.875rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.chat-input:focus {
    outline: none;
    border-color: #D32F2F;
    box-shadow: 0 0 0 3px rgba(211, 47, 47, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

/* Enhanced button styling */
.chat-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.chat-button:hover {
    background-color: rgba(211, 47, 47, 0.1);
    transform: scale(1.1);
}

.chat-button:active {
    transform: scale(0.95);
}
</style>
@endif
<link href="{{ asset('assets/css/toastr.min.css') }}" rel="stylesheet" />