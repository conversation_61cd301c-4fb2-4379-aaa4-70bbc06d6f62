.img-box {
    /* position: relative; */
    width: auto !important;
    height: 50px;
    /* padding: 25px; */
}
.width-auto {
    width: auto !important;
    height: auto !important;
}

.img-box img {
    /* position: absolute; */
    max-width: 100%;
    max-height: 100%;
    /* Ensure the image covers the entire container */
}
.image-container {
    margin-bottom: 20px;
}

.image-container a {
    display: block;
    overflow: hidden;
}

.image-container img {
    transition: transform 0.3s ease-in-out;
}

.image-container a:hover img {
    transform: scale(1.1);
}

#loading-overlay {
    position: fixed;

    top: 0;

    left: 0;

    width: 100%;

    height: 100%;

    background-color: rgba(0, 0, 0, 0.5);
    /* semi-transparent black */

    z-index: 9999;
    /* Ensure it's on top of other elements */

    display: none;
    /* Initially hidden */
}

#loading-indicator {
    position: absolute;

    top: 50%;

    left: 50%;

    transform: translate(-50%, -50%);
}

#loading-indicator img {
    width: 300px;
    /* Adjust the width as needed */
    height: auto;
    /* Maintain aspect ratio */
}

.icon-size {
    width: 50px;
    height: 50px;
}

.list-unstyled li a {
    position: relative;
    text-decoration: none;
}

.list-unstyled li a::after {
    content: "";
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: #fff;
    /* Change this to your desired color */
    transition: width 0.3s ease-in-out;
}

.list-unstyled li a:hover::after {
    width: 100%;
}

.lottie {
    width: 100%;

/* Hero Section Floating Cards Styles */
.hero-images-section {
    position: relative;
    overflow: hidden;
}

.floating-card {
    position: absolute;
    z-index: 2;
    transition: all 0.3s ease;
}

.floating-card img {
    max-width: 100%;
    height: auto;
    border-radius: 1rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.floating-shadow {
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2) !important;
}

/* Desktop positioning */
.floating-card.top-right {
    top: 10%;
    right: 5%;
    width: 200px;
    animation: float-top-right 6s ease-in-out infinite;
}

.floating-card.bottom-left {
    bottom: 10%;
    left: 5%;
    width: 180px;
    animation: float-bottom-left 6s ease-in-out infinite 3s;
}

/* Floating animations */
@keyframes float-top-right {
    0%, 100% { transform: translateY(0px) translateX(0px); }
    50% { transform: translateY(-10px) translateX(5px); }
}

@keyframes float-bottom-left {
    0%, 100% { transform: translateY(0px) translateX(0px); }
    50% { transform: translateY(10px) translateX(-5px); }
}

/* Main image container */
.main-image-container {
    position: relative;
    z-index: 1;
    max-width: 600px;
}

.main-image-container img {
    width: 100%;
    height: auto;
    border-radius: 1rem;
}

/* Tablet styles */
@media (max-width: 1024px) {
    .floating-card.top-right {
        width: 160px;
        top: 8%;
        right: 3%;
    }

    .floating-card.bottom-left {
        width: 140px;
        bottom: 8%;
        left: 3%;
    }
}

/* Mobile styles */
@media (max-width: 768px) {
    .hero-images-section {
        padding: 2rem 0 !important;
    }

    .floating-card {
        position: static;
        display: none; /* Hide floating cards on mobile */
    }

    .main-image-container {
        max-width: 100%;
        padding: 0 1rem;
    }

    .main-image-container img {
        border-radius: 0.75rem;
    }

    /* Show floating cards as stacked images on mobile */
    .hero-images-section .position-relative {
        flex-direction: column;
        gap: 1rem;
    }

    .floating-card {
        display: block;
        position: static;
        width: 100%;
        max-width: 300px;
        margin: 0 auto;
        animation: none;
    }

    .floating-card img {
        width: 100%;
        border-radius: 0.75rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
}

/* Small mobile styles */
@media (max-width: 480px) {
    .hero-images-section {
        padding: 1.5rem 0 !important;
    }

    .main-image-container {
        padding: 0 0.5rem;
    }

    .floating-card {
        max-width: 250px;
    }
}

/* Scroll to Top Button Styles */
#backToTopBtn {
    display: none;
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: linear-gradient(135deg, #D32F2F, #B71C1C);
    color: white;
    border: none;
    border-radius: 50px;
    padding: 12px 20px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 15px rgba(211, 47, 47, 0.3);
    transition: all 0.3s ease;
    z-index: 9999;
    min-width: 80px;
    text-align: center;
}

#backToTopBtn:hover {
    background: linear-gradient(135deg, #B71C1C, #D32F2F);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(211, 47, 47, 0.4);
}

#backToTopBtn.show {
    display: block;
    animation: fadeInUp 0.3s ease;
}

#backToTopBtn .vertical-label {
    display: block;
    font-size: 10px;
    margin-bottom: 2px;
}

#backToTopBtn .arrow-up {
    display: block;
    font-size: 12px;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hide scroll-to-top button on mobile */
@media (max-width: 768px) {
    #backToTopBtn {
        display: none !important;
    }
}

/* Enhanced mobile responsiveness for home hero image */
@media (max-width: 768px) {
    .main-image-container {
        max-width: 100%;
        padding: 0 0.5rem;
    }

    .main-image-container img {
        width: 100%;
        height: auto;
        border-radius: 0.75rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    }

    .hero-images-section {
        padding: 1.5rem 0 !important;
    }

    .hero-images-section .container {
        padding: 0 1rem;
    }

    .hero-images-section .col-12.col-lg-10 {
        padding: 0;
    }
}

/* Ensure header stays fixed on scroll */
.navbar.fixed-top {
    position: fixed !important;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1030;
    background-color: #ffffff !important;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

/* Custom Translation Globe SVG Icon Styling */
.translation-globe-icon {
    width: 20px !important;
    height: 20px !important;
    display: inline-block;
    vertical-align: middle;
    color: #333333;
    transition: all 0.3s ease;
}

.translation-globe-icon:hover {
    color: #D32F2F;
    transform: scale(1.1);
}

/* Front-end navbar globe icon */
.nav-link .translation-globe-icon {
    color: #333333;
}

.nav-link:hover .translation-globe-icon {
    color: #D32F2F;
}

/* Admin panel globe icon */
.btn-outline-primary .translation-globe-icon {
    color: #6f42c1;
}

.btn-outline-primary:hover .translation-globe-icon {
    color: #ffffff;
}

/* Ensure language button maintains consistent styling */
.icon-only svg,
.icon-only img {
    width: 20px !important;
    height: 20px !important;
    display: inline-block;
    vertical-align: middle;
}

/* Features Carousel Mobile Responsive Styles */
.carousel-section {
    padding: 3rem 0;
}

.main-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 3rem;
    line-height: 1.2;
}

.carousel-container {
    max-width: 1200px;
    margin: 0 auto;
}

.carousel-content {
    display: flex;
    align-items: center;
    gap: 2rem;
    padding: 2rem;
}

.carousel-image {
    flex: 1;
    max-width: 500px;
}

.carousel-image img {
    width: 100%;
    height: auto;
    border-radius: 1rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.carousel-text {
    flex: 1;
    padding-left: 2rem;
}

.carousel-text h5 {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
}

.carousel-text p {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #666;
    margin: 0;
}

/* Progress indicator styles */
.carousel-progress-indicator {
    margin-top: 2rem;
}

.progress-bar-container {
    width: 200px;
    height: 4px;
    background-color: #e0e0e0;
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar-active {
    height: 100%;
    background: linear-gradient(90deg, #9747FF, #D32F2F);
    border-radius: 2px;
    transition: width 0.3s ease;
}

/* Tablet responsive styles */
@media (max-width: 1024px) {
    .main-title {
        font-size: 2.2rem;
    }

    .carousel-content {
        gap: 1.5rem;
        padding: 1.5rem;
    }

    .carousel-text {
        padding-left: 1rem;
    }

    .carousel-text h5 {
        font-size: 1.5rem;
    }

    .carousel-text p {
        font-size: 1rem;
    }
}

/* Mobile responsive styles */
@media (max-width: 768px) {
    .carousel-section {
        padding: 2rem 0;
    }

    .main-title {
        font-size: 1.8rem;
        margin-bottom: 2rem;
        padding: 0 1rem;
    }

    .carousel-content {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
        padding: 1rem;
    }

    .carousel-image {
        max-width: 100%;
        order: 2;
    }

    .carousel-text {
        padding-left: 0;
        order: 1;
    }

    .carousel-text h5 {
        font-size: 1.4rem;
        margin-bottom: 0.75rem;
    }

    .carousel-text p {
        font-size: 0.95rem;
        line-height: 1.5;
    }

    /* Add mobile navigation dots */
    .carousel-indicators {
        position: static;
        margin-top: 1.5rem;
        margin-bottom: 0;
    }

    .carousel-indicators [data-bs-target] {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin: 0 4px;
        background-color: #ccc;
        border: none;
        opacity: 0.5;
    }

    .carousel-indicators .active {
        background-color: #9747FF;
        opacity: 1;
    }

    /* Hide progress bar on mobile, show dots instead */
    .carousel-progress-indicator {
        display: none;
    }
}

/* Small mobile styles */
@media (max-width: 480px) {
    .main-title {
        font-size: 1.6rem;
        line-height: 1.3;
    }

    .carousel-content {
        padding: 0.75rem;
    }

    .carousel-text h5 {
        font-size: 1.25rem;
    }

    .carousel-text p {
        font-size: 0.9rem;
    }
}
    height: 500px;
    margin-bottom: 10px;
}

.contact-cta-section {
    background-image: url("/assets/front-end/img/gallery/waves-white.svg");
    background-size: cover;
    background-position: center;
    padding: 40px 20px;
}

.cta-wrapper {
    max-width: 1200px;
    margin: 0 auto;
}

.details-wrapper h2 {
    color: #fff;
    font-size: 32px;
    margin-bottom: 10px;
}

.details-wrapper p {
    color: #fff;
    font-size: 18px;
    margin-bottom: 0;
}
.z-index-frontend {
    position: relative;
    z-index: 3;
}

/* Custom CSS to make next and previous buttons black */
.carousel-control-prev-icon,
.carousel-control-next-icon {
    filter: invert(100%);
}

.carousel-control-prev,
.carousel-control-next {
    color: black;
}

.asterisk {
    color: red;
}
.btn-btn-primary {
    color: #11235a;
}

.image-box-85 {
    width: 85%;
    height: 85%;
    display: flex;
    flex-wrap: nowrap;
    align-content: center;
    justify-content: center;
    align-items: center;
}
.image-box-85 img {
    max-width: 100%;
    max-height: 100%;
}

/* Settings Trigger - Left Side Center */
.settings-trigger {
    position: fixed;
    top: 50%;
    left: 20px;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    background: linear-gradient(to bottom, #b0b0b0 2%, #000 95%);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 18px;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1001;
    display: flex;
    align-items: center;
    justify-content: center;
}
.settings-trigger.hidden {
    display: none !important;
}
.settings-trigger:hover {
    transform: translateY(-50%) translateX(2px) rotate(90deg);
}
.settings-trigger:active {
    transform: translateY(-50%) translateX(0) rotate(90deg) scale(0.95);
}

/* Settings Drawer */
.settings-drawer {
    position: fixed;
    top: 0;
    left: 0;
    width: 320px;
    height: 100vh;
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(20px);
    border-right: 1px solid rgba(255,255,255,0.2);
    box-shadow: 10px 0 30px rgba(0,0,0,0.1);
    transform: translateX(-100%);
    transition: transform 0.4s cubic-bezier(0.4,0,0.2,1);
    z-index: 1000;
    overflow-y: auto;
}
.settings-drawer.open {
    transform: translateX(0);
}
.drawer-header {
    padding: 25px 20px 20px;
    border-bottom: 1px solid rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    background: rgba(255,255,255,0.95);
    z-index: 10;
}
.drawer-title {
    display: flex;
    align-items: center;
    gap: 12px;
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #2d3748;
}
.drawer-title i {
    color: #667eea;
}
.close-drawer {
    position: absolute;
    top: 25px;
    right: 20px;
    background: none;
    border: none;
    font-size: 20px;
    color: #718096;
    cursor: pointer;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}
.close-drawer:hover {
    background: rgba(0,0,0,0.1);
    color: #2d3748;
}
.drawer-content {
    padding: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 85%;
    overflow-y: hidden;
}
.settings-section {
    padding: 25px 20px;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    max-width: 280px;
}
.section-title {
    font-size: 14px;
    font-weight: 600;
    color: #4a5568;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}
.theme-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
}
.theme-option {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4,0,0.2,1);
    border: 2px solid transparent;
    background: rgba(255,255,255,0.5);
    position: relative;
    overflow: hidden;
}
.theme-option::before {
    content: "";
    position: absolute;
    top: 0; left: -100%;
    width: 100%; height: 100%;
    background: linear-gradient(90deg,transparent,rgba(102,126,234,0.1),transparent);
    transition: left 0.6s ease;
}
.theme-option:hover::before { left: 100%; }
.theme-option:hover {
    background: rgba(102,126,234,0.05);
    border-color: rgba(102,126,234,0.2);
    transform: translateX(5px);
}
.theme-option.active {
    background: linear-gradient(135deg,rgba(102,126,234,0.1),rgba(118,75,162,0.1));
    border-color: #667eea;
    box-shadow: 0 4px 15px rgba(102,126,234,0.2);
}
.theme-icon {
    width: 45px; height: 45px; border-radius: 10px;
    display: flex; align-items: center; justify-content: center;
    font-size: 18px; color: white; flex-shrink: 0;
}
.theme-modern .theme-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.theme-classic .theme-icon {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    color: #8b4513;
}
.theme-info { flex: 1; }
.theme-name { font-size: 16px; font-weight: 600; color: #2d3748; margin-bottom: 4px; }
.theme-description { font-size: 13px; color: #718096; line-height: 1.4; }
.drawer-overlay {
    position: fixed; top: 0; left: 0; width: 100%; height: 100%;
    background: rgba(0,0,0,0.3);
    opacity: 0; visibility: hidden;
    transition: all 0.3s ease;
    z-index: 999;
}
.drawer-overlay.active { opacity: 1; visibility: visible; }

/* Responsive */
@media (max-width: 768px) {
    .settings-drawer { border-radius: 0 16px 16px 0; box-shadow: 4px 0 15px rgba(0,0,0,0.2); }
    .settings-trigger { top: 95%; }
    .settings-drawer { width: 260px; }
}

/* Animation for settings icon */
@keyframes rotate {
    from { transform: translateY(-50%) rotate(0deg);}
    to { transform: translateY(-50%) rotate(360deg);}
}
.settings-trigger.rotating { animation: rotate 0.6s ease-in-out; }
